# frozen_string_literal: true

# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Clear existing agents to avoid duplicates when re-seeding
Rails.logger.debug 'Clearing existing agents...'
Agent.destroy_all

# Create default agents
Rails.logger.debug 'Creating agents...'

agents = [
  {
    name: 'Llama 3.2',
    description: 'Latest Llama model with excellent general capabilities. Great for conversations and problem-solving.',
    avatar_url: 'https://seeklogo.com/images/L/llama-meta-logo-E629D6C2F5-seeklogo.com.png',
    configuration: {
      model: 'llama3.2',
      system_prompt: 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses.'
    }
  },
  {
    name: 'Llama 3.1',
    description: 'Powerful Llama model with strong reasoning capabilities. Excellent for complex tasks.',
    avatar_url: 'https://seeklogo.com/images/L/llama-meta-logo-E629D6C2F5-seeklogo.com.png',
    configuration: {
      model: 'llama3.1',
      system_prompt: 'You are an intelligent AI assistant focused on providing detailed and accurate information.'
    }
  },
  {
    name: 'Code Llama',
    description: 'Specialized in programming help, code review, and technical problem-solving across multiple languages.',
    avatar_url: 'https://cdn-icons-png.flaticon.com/512/6295/6295417.png',
    configuration: {
      model: 'codellama',
      system_prompt: 'You are an expert programming assistant. Focus on providing accurate, efficient code solutions and explanations. Always include comments in your code examples.'
    }
  },
  {
    name: 'Mistral',
    description: 'Efficient and capable model with strong performance across various tasks.',
    avatar_url: 'https://cdn-icons-png.flaticon.com/512/8637/8637099.png',
    configuration: {
      model: 'mistral',
      system_prompt: 'You are a knowledgeable AI assistant. Provide concise yet comprehensive responses.'
    }
  },
  {
    name: 'Gemma',
    description: "Google's open model with excellent instruction following and creative capabilities.",
    avatar_url: 'https://cdn-icons-png.flaticon.com/512/2991/2991148.png',
    configuration: {
      model: 'gemma',
      system_prompt: 'You are a creative and helpful AI assistant. Focus on being engaging and informative in your responses.'
    }
  }
]

agents.each do |agent_data|
  agent = Agent.create!(agent_data)
  Rails.logger.debug { "Created agent: #{agent.name}" }
end

Rails.logger.debug 'Seed completed successfully!'
