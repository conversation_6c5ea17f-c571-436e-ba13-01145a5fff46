# frozen_string_literal: true

# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Clear existing agents to avoid duplicates when re-seeding
puts "Clearing existing agents..."
Agent.destroy_all

# Create default agents
puts "Creating agents..."

agents = [
  {
    name: "<PERSON>",
    description: "A helpful, harmless, and honest AI assistant. <PERSON> excels at thoughtful conversations and creative tasks.",
    avatar_url: "https://storage.googleapis.com/anthropic-public/claude-avatar.png",
    configuration: {
      model: "claude-3-opus-20240229",
      temperature: 0.7,
      max_tokens: 4000
    }
  },
  {
    name: "GPT-4",
    description: "Advanced AI model with broad knowledge and capabilities. Great for complex reasoning and detailed explanations.",
    avatar_url: "https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/ChatGPT_logo.svg/1200px-ChatGPT_logo.svg.png",
    configuration: {
      model: "gpt-4-turbo",
      temperature: 0.7,
      max_tokens: 4000
    }
  },
  {
    name: "Llama 3",
    description: "Open-source large language model from Meta. Balanced performance with efficient resource usage.",
    avatar_url: "https://seeklogo.com/images/L/llama-meta-logo-E629D6C2F5-seeklogo.com.png",
    configuration: {
      model: "llama-3-70b-instruct",
      temperature: 0.7,
      max_tokens: 4000
    }
  },
  {
    name: "Coding Assistant",
    description: "Specialized in programming help, code review, and technical problem-solving across multiple languages.",
    avatar_url: "https://cdn-icons-png.flaticon.com/512/6295/6295417.png",
    configuration: {
      model: "claude-3-opus-20240229",
      temperature: 0.2,
      max_tokens: 8000,
      system_prompt: "You are an expert programming assistant. Focus on providing accurate, efficient code solutions and explanations."
    }
  },
  {
    name: "Creative Writer",
    description: "Helps with creative writing, storytelling, and generating engaging content in various styles.",
    avatar_url: "https://cdn-icons-png.flaticon.com/512/5390/5390264.png",
    configuration: {
      model: "gpt-4-turbo",
      temperature: 0.9,
      max_tokens: 8000,
      system_prompt: "You are a creative writing assistant with expertise in storytelling, poetry, and various literary styles."
    }
  }
]

agents.each do |agent_data|
  agent = Agent.create!(agent_data)
  puts "Created agent: #{agent.name}"
end

puts "Seed completed successfully!"
