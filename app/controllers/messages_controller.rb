class MessagesController < ApplicationController
  before_action :set_chat
  
  def create
    @message = @chat.messages.new(message_params)
    @message.role = "user"
    
    if @message.save
      # Create AI response
      @response = @chat.messages.create!(
        role: "assistant",
        content: generate_ai_response(@message.content, @chat.agent)
      )
      
      redirect_to @chat
    else
      redirect_to @chat, alert: "Failed to send message"
    end
  end
  
  private
  
  def set_chat
    @chat = Chat.find(params[:chat_id])
  end
  
  def message_params
    params.require(:message).permit(:content)
  end
  
  def generate_ai_response(user_message, agent)
    # This is a placeholder. In a real app, you would call an AI API here
    "This is a simulated response from #{agent.name}. You said: #{user_message}"
  end
end