# frozen_string_literal: true

class ChatsController < ApplicationController
  before_action :set_chat, only: %i[show destroy]
  before_action :set_user_cookie

  def index
    @chats = Chat.where(user_cookie: @user_cookie).order(created_at: :desc)
    @agents = Agent.all
  end

  def show
    @messages = @chat.messages.ordered
    @chats = Chat.where(user_cookie: @user_cookie).order(created_at: :desc)
  end

  def create
    @chat = Chat.new(chat_params)
    @chat.user_cookie = @user_cookie

    # Set default title if not provided
    if @chat.title.blank? && @chat.agent_id.present?
      agent = Agent.find(@chat.agent_id)
      @chat.title = "Chat with #{agent.name}"
    end

    if @chat.save
      redirect_to @chat
    else
      Rails.logger.error "Chat creation failed: #{@chat.errors.full_messages.join(', ')}"
      redirect_to chats_path, alert: "Failed to create chat: #{@chat.errors.full_messages.join(', ')}"
    end
  end

  def destroy
    @chat.destroy
    redirect_to chats_path, notice: 'Chat was deleted'
  end

  private

  def set_chat
    @chat = Chat.find(params[:id])
  end

  def set_user_cookie
    @user_cookie = cookies[:user_id] ||= SecureRandom.uuid
  end

  def chat_params
    params.require(:chat).permit(:agent_id, :title)
  end
end
