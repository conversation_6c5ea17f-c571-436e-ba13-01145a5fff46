# frozen_string_literal: true

class GenerateAiResponseJob < ApplicationJob
  queue_as :default

  def perform(chat_id, user_message)
    chat = Chat.find(chat_id)
    previous_messages = chat.messages.ordered

    # Generate AI response using Ollama
    ollama_service = OllamaService.new
    ai_response = ollama_service.generate_response(chat.agent, previous_messages, user_message)

    # Create and save the AI response message
    response_message = chat.messages.create!(
      role: 'assistant',
      content: ai_response
    )

    # Broadcast the AI response
    ActionCable.server.broadcast("chat_#{chat.id}",
                                 { type: 'message',
                                   message: {
                                     id: response_message.id,
                                     content: response_message.content,
                                     role: response_message.role,
                                     created_at: response_message.created_at.strftime('%H:%M')
                                   } })
  rescue StandardError => e
    Rails.logger.error "AI Response Job failed: #{e.message}"
    
    # Send error message to user
    error_message = chat.messages.create!(
      role: 'assistant',
      content: "I'm sorry, I encountered an error while processing your request. Please try again."
    )

    ActionCable.server.broadcast("chat_#{chat.id}",
                                 { type: 'message',
                                   message: {
                                     id: error_message.id,
                                     content: error_message.content,
                                     role: error_message.role,
                                     created_at: error_message.created_at.strftime('%H:%M')
                                   } })
  end
end
