# == Schema Information
#
# Table name: chats
#
#  id          :bigint           not null, primary key
#  title       :string           not null
#  user_cookie :string           not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  agent_id    :bigint           not null
#
# Indexes
#
#  index_chats_on_agent_id     (agent_id)
#  index_chats_on_user_cookie  (user_cookie)
#
# Foreign Keys
#
#  fk_rails_...  (agent_id => agents.id)
#
class Chat < ApplicationRecord
  belongs_to :agent
  has_many :messages, dependent: :destroy

  validates :title, presence: true

  before_create :set_default_title

  private

  def set_default_title
    if title.blank?
      self.title = agent ? "Chat with #{agent.name}" : "New Chat"
    end
  end
end
