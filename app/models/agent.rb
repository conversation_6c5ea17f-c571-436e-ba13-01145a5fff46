# == Schema Information
#
# Table name: agents
#
#  id            :bigint           not null, primary key
#  avatar_url    :string           not null
#  configuration :jsonb
#  description   :text             not null
#  name          :string           not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#
class Agent < ApplicationRecord
  has_many :chats
  
  validates :name, presence: true
  validates :description, presence: true
  validates :avatar_url, presence: true
end
