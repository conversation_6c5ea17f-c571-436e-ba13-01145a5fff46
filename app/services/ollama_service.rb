# frozen_string_literal: true

class OllamaService
  include HTTParty

  BASE_URL = ENV.fetch('OLLAMA_URL', 'http://localhost:11434')

  def initialize
    @connection = Faraday.new(url: BASE_URL) do |faraday|
      faraday.request :json
      faraday.response :json
      faraday.adapter Faraday.default_adapter
    end
  end

  def generate_response(agent, messages, user_message)
    model_name = agent.configuration&.dig('model') || 'llama2'

    # Build conversation history
    conversation = build_conversation(messages, user_message)

    begin
      response = @connection.post('/api/generate') do |req|
        req.body = {
          model: model_name,
          prompt: conversation,
          stream: false
        }
      end

      if response.success?
        response.body['response']
      else
        "I'm sorry, I'm having trouble connecting to the AI service right now. Please try again later."
      end
    rescue StandardError => e
      Rails.logger.error "Ollama API error: #{e.message}"
      "I'm sorry, I encountered an error while processing your request. Please try again."
    end
  end

  def list_models
    response = @connection.get('/api/tags')
    if response.success?
      response.body['models']&.pluck('name') || []
    else
      []
    end
  rescue StandardError => e
    Rails.logger.error "Ollama models list error: #{e.message}"
    []
  end

  private

  def build_conversation(messages, user_message)
    conversation = ''

    # Add system prompt if agent has one
    if messages.first&.chat&.agent&.configuration&.dig('system_prompt')
      conversation += "System: #{messages.first.chat.agent.configuration['system_prompt']}\n\n"
    end

    # Add previous messages
    messages.each do |message|
      role = message.role == 'user' ? 'Human' : 'Assistant'
      conversation += "#{role}: #{message.content}\n\n"
    end

    # Add current user message
    conversation += "Human: #{user_message}\n\nAssistant:"

    conversation
  end
end
